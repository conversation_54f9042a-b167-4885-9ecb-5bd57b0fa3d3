import { Context } from 'telegraf';
import createDebug from 'debug';

const debug = createDebug('bot:play');
const codes: { [groupId: string]: string } = {};

const generateCode = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

const setCodeForGroup = (groupId: string, code?: string): string => {
  const newCode = code || generateCode();
  codes[groupId] = newCode;
  debug(`Set code ${newCode} for group ${groupId}`);
  return newCode;
};

const getCodeForGroup = (groupId: string): string | undefined => {
  return codes[groupId];
};

const deleteCodeForGroup = (groupId: string): boolean => {
  if (codes[groupId]) {
    delete codes[groupId];
    debug(`Deleted code for group ${groupId}`);
    return true;
  }
  return false;
};

const newcode = () => async (ctx: Context) => {
  debug(`Triggered "newcode" command`);

  const chatId = ctx.chat?.id.toString();
  if (!chatId) {
    await ctx.reply('Error: Tidak dapat mengidentifikasi chat ID');
    return;
  }

  setCodeForGroup(chatId);
  const message = "🎯 *Code baru telah dibuat!*"

  await ctx.replyWithMarkdownV2(message);
};

const play = () => async (ctx: Context) => {
  const text = ctx.text;
  debug(`Triggered "play" command with text ${text}`);
  if (!text) return;

  if (!/^\d{4}$/.test(text)) return;

  const chatId = ctx.chat?.id.toString();
  if (!chatId) {
    await ctx.reply('Tidak dapat mengidentifikasi chat ID');
    return;
  }

  const code = getCodeForGroup(chatId);
  if (!code) {
    await ctx.reply('Tidak ada code untuk group ini', { reply_parameters: { message_id: ctx.msgId! }});
    return;
  }

  if (text === code) {
    await ctx.reply('Selamat! Anda berhasil menebak code!', { reply_parameters: { message_id: ctx.msgId! }});
    setCodeForGroup(chatId);
  } else {
    let correctCount = 0;
    for (let i = 0; i < 4; i++) {
      if (text[i] === code[i]) {
        correctCount++;
      }
    }
    
    if (correctCount > 0) {
      await ctx.reply(`Ada ${correctCount} angka yang benar!`, { reply_parameters: { message_id: ctx.msgId! }});
    } else {
      await ctx.reply('Tidak ada angka yang benar!', { reply_parameters: { message_id: ctx.msgId! }});
    }
  }
};

export {
  newcode,
  play,
  generateCode,
  setCodeForGroup,
  getCodeForGroup,
  deleteCodeForGroup,
  codes
};
